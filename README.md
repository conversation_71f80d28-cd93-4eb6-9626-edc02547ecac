# WhisperX Video Transcriber

A Streamlit app that transcribes videos to text and generates subtitles using WhisperX via the Replicate API.

## Features

- High-quality transcription using WhisperX
- Automatic audio preprocessing with noise reduction
- Customizable subtitle formatting
- Support for large files (automatic chunking)
- Speaker diarization (optional)
- Multiple output formats (TXT, SRT, VTT)

## Deployment Requirements

### For Streamlit Cloud

This app requires the following files for proper deployment:

1. **requirements.txt** - Python dependencies
2. **packages.txt** - System packages (contains `ffmpeg`)
3. **.streamlit/secrets.toml** - API configuration

#### Required secrets.toml format:
```toml
[api]
replicate_token = "your_replicate_api_token_here"
```

### Local Development

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Install FFmpeg:
   - **Windows**: Download from https://www.gyan.dev/ffmpeg/builds/
   - **macOS**: `brew install ffmpeg`
   - **Ubuntu/Debian**: `sudo apt install ffmpeg`

3. Set up your Replicate API token in `.streamlit/secrets.toml`

4. Run the app:
   ```bash
   streamlit run app.py
   ```

## Usage

1. Upload an MP4 video file
2. Configure transcription settings in the sidebar
3. Click "Transcribe" to process the video
4. Download the results in your preferred format

## Troubleshooting

### FFmpeg Not Found Error

If you see "Audio preprocessing failed with error: [Errno 2] No such file or directory: 'ffmpeg'":

1. **For Streamlit Cloud**: Make sure you have a `packages.txt` file with `ffmpeg` listed, then redeploy
2. **For local development**: Install FFmpeg using the instructions above

The app will show the FFmpeg status in the sidebar to help diagnose issues.
