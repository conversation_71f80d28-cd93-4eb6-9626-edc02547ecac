Summary table of all WhisperX model-specific input parameters exposed via the Replicate API (using the sidedwards/whisperx variant) and how each controls the transcription outputs:

| Parameter                           | Default | Options                  | Description                                                                                                                                                 |
| ----------------------------------- | ------- | ------------------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **audio\_file**                     | *n/a*   | URI string               | Specifies the audio source (mp3/mp4) to transcribe; drives the creation of both `segments` and `detected_language` in the output ([Replicate][1])           |
| **audio\_blob**                     | *n/a*   | Data URI string          | Alternate way to supply audio (mp3/mp4) as a blob; likewise feeds into `segments` and `detected_language` ([Replicate][1])                                  |
| **language**                        | `None`  | ISO 639-1 code or `None` | If set, forces transcription into that language; otherwise auto-detects—affects both transcript text and the `detected_language` field ([Replicate][1])     |
| **language\_detection\_min\_prob**  | `0`     | Number (0–1)             | Minimum confidence threshold for auto language detection; higher values make detection stricter, influencing the final `detected_language` ([Replicate][1]) |
| **language\_detection\_max\_tries** | `5`     | Integer ≥1               | Max recursive attempts at language detection before settling on the most probable, populating `detected_language` ([Replicate][1])                          |
| **initial\_prompt**                 | `""`    | String                   | Seed text prompt for the first window of transcription; can bias the first few `segments`’ transcription ([Replicate][1])                                   |
| **batch\_size**                     | `64`    | Integer ≥1               | Number of audio chunks processed in parallel; improves throughput but does not alter transcript content ([Replicate][1])                                    |
| **temperature**                     | `0`     | Number ≥0                | Sampling randomness for text generation; higher values can produce more varied wordings in `segments` ([Replicate][1])                                      |
| **vad\_onset**                      | `0.5`   | Number 0–1               | Voice Activity Detection start threshold; controls where speech segments begin in the `segments` array ([Replicate][1])                                     |
| **vad\_offset**                     | `0.363` | Number 0–1               | Voice Activity Detection end threshold; controls where speech segments end in the `segments` array ([Replicate][1])                                         |
| **align\_output**                   | `false` | Boolean `true`/`false`   | When `true`, forces word-level timestamps via forced alignment; populates `segments[].words` with per-word timing ([Replicate][1])                          |
| **diarization**                     | `false` | Boolean `true`/`false`   | When `true`, runs speaker diarization; populates `segments[].speaker` labels in the output ([Replicate][1])                                                 |
| **min\_speakers**                   | *n/a*   | Integer >1 or blank      | If diarization is on, sets the minimum number of speakers to detect; influences speaker grouping in `segments` ([Replicate][1])                             |
| **max\_speakers**                   | *n/a*   | Integer >1 or blank      | If diarization is on, sets the maximum number of speakers to detect; influences speaker grouping in `segments` ([Replicate][1])                             |
| **huggingface\_access\_token**      | *n/a*   | String                   | Required to enable diarization via PyAnnote models; does not appear in JSON but enables speaker metadata ([Replicate][1])                                   |
| **debug**                           | `false` | Boolean `true`/`false`   | If `true`, prints detailed compute/inference times and memory usage to logs; does not modify the JSON response ([Replicate][1])                             |

> **Output schema:**
> The model returns an object with two top-level properties:
>
> * **segments**: an array of transcript segments (with `start`, `end`, `text`, plus optional `words` and `speaker` fields depending on `align_output` and `diarization`)
> * **detected\_language**: the ISO code of the language detected or forced by `language` ([Replicate][1])

[1]: https://replicate.com/sidedwards/whisperx/api "sidedwards/whisperx – API reference"
========================================================
WhisperX itself does **not** impose any limits on the number of words, letters, or “lines” per segment—those concepts belong to subtitle-formatting tools, not the transcription model. All of WhisperX’s segmentation is based on audio-chunking and VAD (voice activity detection) thresholds, not on character or line counts in the text.

* **No word/character-count parameters**
  Parameters like `vad_onset`/`vad_offset` (which control where speech starts and stops) and the implicit chunk/window size (driven by the underlying Whisper model’s frame length) determine how the audio is split into segments. There is no setting such as “max\_words\_per\_segment” or “max\_chars” in the API.

* **No “max segment lines” parameter**
  WhisperX returns a flat array of `segments`, each with `start`, `end`, and `text` (plus optional `words` and `speaker` fields). It makes no attempt to wrap text into lines or limit the number of lines per caption block.

* **Subtitle formatting is a post-processing step**
  If you need to produce SRT, VTT or any other subtitle format with line-length caps (e.g. 32 characters per line) and a maximum of two lines per cue, you must take WhisperX’s raw `segments` and run them through a subtitle-generation library or your own logic (for example, using the Python packages `srt` or `pysubs2`) to:

  1. **Split text** on spaces or punctuation into chunks that respect your character limits.
  2. **Re-assign timestamps** (you can use segment `start`/`end` or per-word times if you enabled `align_output`).
  3. **Enforce line-count rules** (e.g. no more than two lines per caption).

In short, WhisperX focuses purely on transcribing and (optionally) aligning words to time; subtitle styling, wrapping, and line-count constraints all live in whatever tool or script you use to convert its output into `.srt`/`.vtt`.
========================================================
WhisperX’s output **does** include punctuation (periods, commas, colons, ellipses, question marks, etc.) exactly as the underlying Whisper model decodes it. There is **no** Replicate API or WhisperX-specific parameter to turn punctuation on or off. Punctuation is inserted (or omitted) solely by Whisper’s language model during transcription, and WhisperX simply returns that punctuated text in each segment’s `text` field ([GitHub][1], [Replicate][2]).

If you need to remove, alter, or otherwise post-process punctuation (for example, to meet subtitle line-length requirements or stylistic guidelines), you must do so after receiving WhisperX’s raw segments—e.g. with a regex pass or a dedicated punctuation-restoration/customization tool.

[1]: https://github.com/openai/whisper/discussions/589?utm_source=chatgpt.com "Force no punctuation · openai whisper · Discussion #589 - GitHub"
[2]: https://replicate.com/keeandev/whisperx/api?utm_source=chatgpt.com "Run keeandev/whisperx with an API - Replicate"
========================================================
The following “max\_…” options live only in the local-CLI version of WhisperX (i.e. in `whisperx/__main__.py`) and are **not** exposed via the Replicate API. They only apply when you run `whisperx.transcribe` with alignment enabled (you cannot use them with `--no_align`). Here’s what each does:

| Parameter                                                                                                      | Type                      | Default      | Applies only with `--align_output` | What it controls                                                                                                                                                                   |
| -------------------------------------------------------------------------------------------------------------- | ------------------------- | ------------ | ---------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `--max_line_width`                                                                                             | int or None               | `None`       | Yes                                | After forced-alignment, when writing out text it will hard-wrap each line at **N** characters.  If you leave it `None`, no wrapping is done and you get one long line per segment. |
| `--max_line_count`                                                                                             | int or None               | `None`       | Yes                                | Limits the **number of lines** per caption segment.  If after wrapping you have more than **M** lines, the overflow lines get merged or dropped so you never exceed **M**.         |
| `--segment_resolution`                                                                                         | `"sentence"` \| `"chunk"` | `"sentence"` | Yes                                | Chooses how words are grouped into segments **before** line-wrapping:                                                                                                              |
| • `"sentence"`: use a sentence-splitter (e.g. NLTK’s sent\_tokenize) so each segment is a full sentence.       |                           |              |                                    |                                                                                                                                                                                    |
| • `"chunk"`: break at fixed audio-chunk/window boundaries (as driven by your `--chunk_size` and VAD settings). |                           |              |                                    |                                                                                                                                                                                    |

Because none of these exist in the Replicate API, if you need to enforce character- or line-limits (or to group by sentences) you must post-process the raw `segments` array yourself (e.g. with an SRT/VTT library or a simple wrap+truncate script).

*(Source: the WhisperX CLI args in `whisperx/__main__.py` on GitHub)* ([github.com][1])

[1]: https://github.com/m-bain/whisperx/blob/main/whisperx/__main__.py "whisperX/whisperx/__main__.py at main · m-bain/whisperX · GitHub"
========================================================
When you run WhisperX via the Replicate API you get back a JSON object that, depending on your parameters, includes:

* **segments** – an array of transcript blocks, each with

  * `start`/`end` timestamps
  * `text` (complete with punctuation as decoded by Whisper)
  * **and**, if you set `align_output=true`, a `words` list of `{ word, start, end }` entries&#x20;

Once you have that JSON you can feed it into any subtitle‐generation routine (e.g. using Python’s `srt` or `pysubs2` libraries, or your own script) to:

1. **Wrap text** at your chosen character‐width
2. **Enforce a maximum line count** per cue
3. **Re‐assign or refine timings** (using segment or word timestamps)

…and output a properly formatted `.srt` or `.vtt` file with exactly the number of lines and characters per line you need.
======================================================
### Best Practices for Improving Transcription Quality and Reducing Hallucinations in WhisperX on Replicate

Achieving high transcription quality and minimizing hallucinations (i.e., fabricated or repeated text not present in the audio) with WhisperX on Replicate requires a combination of careful audio preparation, optimal model configuration, and robust post-processing. Here are the most effective strategies, based on current research and user experience:

#### **1. Pre-processing: Clean and Segment Audio**

- **Trim and Segment Audio:** Before transcription, trim silences and non-speech segments, and split long recordings into manageable chunks (ideally around 30 seconds). This reduces the cognitive load on the model and helps prevent context drift, which can lead to hallucinations[3][4][5].
- **Voice Activity Detection (VAD):** Use an external VAD model to accurately detect and segment speech regions. WhisperX specifically recommends pre-segmenting audio with VAD, then merging segments to ensure each chunk contains only active speech. This approach improves both speed and accuracy, and is a core part of the WhisperX pipeline[5].
- **Noise Reduction:** Filter out background noise and non-speech sounds before transcription. This can be done with audio editing tools or automated noise reduction algorithms, which help the model focus on actual speech and reduce the risk of hallucinating content.

#### **2. Model and Parameter Selection**

- **Choose the Right Model Version:** Not all Whisper models perform equally across languages and audio types. For some languages or noisy conditions, older or smaller models (e.g., large-v2 instead of large-v3) may yield better results. Test different models on your data to find the best fit[1][2].
- **Set the Correct Language:** Always specify the correct language for the audio. Mismatched language settings are a common cause of hallucinations and poor transcription quality[1].
- **Temperature and Beam Size:** Use a low temperature (e.g., 0.1 or lower) for more deterministic outputs, and experiment with beam size and patience settings. However, avoid using `best_of` with beam search, as it only applies to greedy decoding.
- **Return Timestamps:** Enabling `return_timestamps=True` grounds the model, making it less likely to hallucinate by forcing alignment between text and audio duration. This is especially effective for long-form or chunked transcription[5].

#### **3. Forced Alignment and Post-processing**

- **Forced Alignment:** WhisperX enhances Whisper by adding a forced alignment step using a lightweight phoneme recognition model. This aligns the transcript to the audio at the word level, correcting timing and reducing errors from mis-segmentation or hallucinated text[1][2][5].
- **Chunk Re-transcription:** If hallucinations or repeated segments are detected, re-transcribe only the affected audio chunks. Automated scripts can identify and rerun problematic segments, then merge the corrected results[3].
- **Post-processing:** After transcription, refine the output by correcting punctuation, normalizing terminology, and fixing Unicode or formatting issues. This step can also include human review for high-stakes applications[3].

#### **4. Additional Recommendations**

- **Audio Quality Matters:** High-quality, uncompressed audio (e.g., WAV at 16kHz or higher) yields better results than low-bitrate, compressed formats. If possible, avoid using audio that has undergone multiple lossy compression steps.
- **Speaker Diarization:** For multi-speaker audio, use diarization tools in conjunction with WhisperX to separate speakers before transcription. This prevents merging of different speakers' statements, which can confuse the model and lead to hallucinations[2].
- **Human Supervision:** For critical use cases, always include a human review step. No automated system is entirely free from hallucinations or transcription errors, especially in noisy or complex audio environments.

#### **Summary Table: Key Techniques**

| Technique                        | Purpose                                      | Impact on Hallucinations/Quality         |
|-----------------------------------|----------------------------------------------|------------------------------------------|
| VAD-based segmentation           | Isolate speech, remove silence/noise         | Reduces hallucinations, improves accuracy|
| Forced alignment (WhisperX)      | Aligns transcript to audio at word level     | Corrects timing, reduces errors          |
| Low temperature, correct language| Model configuration                          | More accurate, less hallucination        |
| Return timestamps                | Grounds text to audio duration               | Discourages hallucinated/repeated text   |
| Audio pre-processing             | Clean, denoise, and segment audio            | Improves model focus, reduces errors     |
| Post-processing & human review   | Refine and verify output                     | Catches residual hallucinations          |

#### **Conclusion**

While WhisperX on Replicate significantly improves transcription quality over base Whisper, hallucinations can still occur, especially with long, noisy, or multi-speaker audio. The most effective approach combines careful audio pre-processing (VAD, trimming, denoising), optimal model and parameter selection (language, temperature, timestamps), forced alignment, and robust post-processing (including human review for critical tasks). No single step is sufficient alone; the best results come from integrating these practices throughout your transcription workflow[1][2][3][5].


## Citations
[1] https://github.com/m-bain/whisperX
[2] https://valor-software.com/articles/interview-transcription-using-whisperx-model-part-1
[3] https://cookbook.openai.com/examples/whisper_processing_guide
[4] https://transcribethis.io/blog/speeding_up_whisper_transcriptions_techniques_for_faster_aud.php
[5] https://ar5iv.labs.arxiv.org/html/2303.00747